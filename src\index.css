
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Game Animations */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
  50% { transform: translateY(-20px) rotate(180deg); opacity: 0.7; }
}

@keyframes confetti {
  0% { transform: translateY(-100vh) rotate(0deg); opacity: 1; }
  100% { transform: translateY(100vh) rotate(720deg); opacity: 0; }
}

.animate-shake {
  animation: shake 0.6s ease-in-out;
}

.animate-float {
  animation: float 2s ease-in-out infinite;
}

.animate-confetti {
  animation: confetti 3s linear infinite;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;

    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;

    --popover: 224 71% 4%;
    --popover-foreground: 213 31% 91%;

    --primary: 263 70% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 215 25% 27%;
    --secondary-foreground: 210 40% 98%;

    --muted: 215 25% 27%;
    --muted-foreground: 217.9 10.6% 64.9%;

    --accent: 215 25% 27%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 215 25% 27%;
    --input: 215 25% 27%;
    --ring: 263 70% 50%;

    --sidebar-background: 224 71% 4%;
    --sidebar-foreground: 213 31% 91%;
    --sidebar-primary: 263 70% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 215 25% 27%;
    --sidebar-accent-foreground: 213 31% 91%;
    --sidebar-border: 215 25% 27%;
    --sidebar-ring: 263 70% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    background: light-dark(
      linear-gradient(135deg, #667eea 0%, #764ba2 100%),
      linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)
    );
  }

  .dark body {
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  }

  /* Dark theme neon effects */
  .dark .neon-text {
    color: #00ffff;
    text-shadow: 0 0 5px #00ffff, 0 0 10px #00ffff, 0 0 15px #00ffff;
  }

  .dark .neon-border {
    border: 1px solid #00ffff;
    box-shadow: 0 0 5px #00ffff, inset 0 0 5px #00ffff;
  }

  .dark .neon-glow {
    box-shadow: 0 0 10px #8b5cf6, 0 0 20px #8b5cf6, 0 0 30px #8b5cf6;
  }

  /* Sound Settings Slider Styling */
  .slider::-webkit-slider-thumb {
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    box-shadow: 0 0 2px 0 #555;
    transition: background .15s ease-in-out;
  }

  .slider::-webkit-slider-thumb:hover {
    background: #764ba2;
  }

  .slider::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
    box-shadow: 0 0 2px 0 #555;
    transition: background .15s ease-in-out;
  }

  .slider::-moz-range-thumb:hover {
    background: #764ba2;
  }

  .dark .slider::-webkit-slider-thumb {
    background: #8b5cf6;
  }

  .dark .slider::-webkit-slider-thumb:hover {
    background: #a855f7;
  }

  .dark .slider::-moz-range-thumb {
    background: #8b5cf6;
  }

  .dark .slider::-moz-range-thumb:hover {
    background: #a855f7;
  }
}
